package com.fxiaoke.file.server.web.resolver;

import com.fxiaoke.file.server.domain.model.api.request.SignFileUpRequest;
import com.fxiaoke.file.server.utils.StrUtils;
import jakarta.ws.rs.BadRequestException;
import java.util.ArrayList;
import java.util.List;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

@Component
public class SignFileUpRequestArgumentResolver implements HandlerMethodArgumentResolver {

  // 错误消息模板
  private static final String ERROR_MESSAGE_TEMPLATE = "Missing required headers: %s";

  @Override
  public boolean supportsParameter(MethodParameter parameter) {
    return SignFileUpRequest.class.equals(parameter.getParameterType());
  }

  @Override
  public Object resolveArgument(@NotNull MethodParameter parameter,
      ModelAndViewContainer mavContainer,
      @NotNull NativeWebRequest webRequest,
      WebDataBinderFactory binderFactory) throws MissingRequestHeaderException {

    SignFileUpRequest request = new SignFileUpRequest();
    List<String> missingHeaders = new ArrayList<>();

    request.setAcid(getHeader(webRequest, "acid", missingHeaders));
    request.setResource(getHeader(webRequest, "resource", missingHeaders));
    request.setAk(getHeader(webRequest, "ak", missingHeaders));
    request.setSign(getHeader(webRequest, "sign", missingHeaders));
    request.setFileName(getHeader(webRequest, "filename", missingHeaders));
    request.setDigest(getHeader(webRequest, "digest", missingHeaders));
    request.setUserAgent(getHeader(webRequest, "User-Agent", missingHeaders));

    String expiryStr = getHeader(webRequest, "expiry", missingHeaders);
    String sizeStr = getHeader(webRequest, "size", missingHeaders);

    // 在所有Header都检查完之后，再抛出异常
    if (!missingHeaders.isEmpty()) {
      throw new MissingRequestHeaderException(
          String.format(ERROR_MESSAGE_TEMPLATE, String.join(", ", missingHeaders)),
          parameter);
    }

    try {
      request.setExpiry(Long.parseLong(expiryStr));
      request.setSize(Integer.parseInt(sizeStr));
    } catch (NumberFormatException e) {
      throw new BadRequestException("Invalid number format for header. " + e.getMessage());
    }

    return request;
  }

  private String getHeader(NativeWebRequest webRequest, String headerName, List<String> missingHeaders) {
    String value = webRequest.getHeader(headerName);
    if (StrUtils.isBlank(value)) {
      missingHeaders.add(headerName);
    }
    return value;
  }
}

